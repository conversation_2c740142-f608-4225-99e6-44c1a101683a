{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date);\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function calculateReadingTime(text: string): number {\n  const wordsPerMinute = 200;\n  const words = text.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading = false, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white focus:ring-gray-500',\n      ghost: 'text-gray-300 hover:bg-gray-800 hover:text-white focus:ring-gray-500',\n      destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-2 text-sm',\n      md: 'px-4 py-2 text-base',\n      lg: 'px-6 py-3 text-lg',\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          isLoading && 'cursor-not-allowed',\n          className\n        )}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading ? (\n          <>\n            <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n            </svg>\n            Loading...\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAmG;QAAlG,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;IAC/F,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,sBACb;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,0BACC;;8BACE,6LAAC;oBAAI,WAAU;oBAAkC,MAAK;oBAAO,SAAQ;;sCACnE,6LAAC;4BAAO,WAAU;4BAAa,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,QAAO;4BAAe,aAAY;;;;;;sCACxF,6LAAC;4BAAK,WAAU;4BAAa,MAAK;4BAAe,GAAE;;;;;;;;;;;;gBAC/C;;2BAIR;;;;;;AAIR;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/lib/constants.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'SnapStudy',\n  description: 'Learn from Your Notes Instantly',\n  version: '0.1.0',\n  author: 'SnapStudy Team',\n} as const;\n\nexport const LIMITS = {\n  FREE_PLAN: {\n    maxNotes: 10,\n    maxFlashcards: 50,\n    maxQuizzes: 5,\n    maxImageSize: 5 * 1024 * 1024, // 5MB\n  },\n  PRO_PLAN: {\n    maxNotes: -1, // unlimited\n    maxFlashcards: -1, // unlimited\n    maxQuizzes: -1, // unlimited\n    maxImageSize: 20 * 1024 * 1024, // 20MB\n  },\n} as const;\n\nexport const SUPPORTED_IMAGE_TYPES = [\n  'image/jpeg',\n  'image/jpg',\n  'image/png',\n  'image/webp',\n  'application/pdf',\n] as const;\n\nexport const SUBJECTS = [\n  'Mathematics',\n  'Science',\n  'History',\n  'Literature',\n  'Languages',\n  'Computer Science',\n  'Business',\n  'Medicine',\n  'Law',\n  'Engineering',\n  'Art',\n  'Music',\n  'Other',\n] as const;\n\nexport const DIFFICULTY_LEVELS = ['easy', 'medium', 'hard'] as const;\n\nexport const QUIZ_TYPES = ['multiple-choice', 'true-false', 'fill-blank'] as const;\n\nexport const STUDY_GOALS = [\n  { label: '15 minutes', value: 15 },\n  { label: '30 minutes', value: 30 },\n  { label: '45 minutes', value: 45 },\n  { label: '1 hour', value: 60 },\n  { label: '1.5 hours', value: 90 },\n  { label: '2 hours', value: 120 },\n] as const;\n\nexport const ROUTES = {\n  HOME: '/',\n  DASHBOARD: '/dashboard',\n  NOTES: '/notes',\n  FLASHCARDS: '/flashcards',\n  QUIZZES: '/quizzes',\n  STUDY: '/study',\n  SETTINGS: '/settings',\n  PROFILE: '/profile',\n} as const;\n\nexport const API_ENDPOINTS = {\n  NOTES: '/api/notes',\n  FLASHCARDS: '/api/flashcards',\n  QUIZZES: '/api/quizzes',\n  OCR: '/api/ocr',\n  AI_PROCESS: '/api/ai/process',\n  UPLOAD: '/api/upload',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,QAAQ;AACV;AAEO,MAAM,SAAS;IACpB,WAAW;QACT,UAAU;QACV,eAAe;QACf,YAAY;QACZ,cAAc,IAAI,OAAO;IAC3B;IACA,UAAU;QACR,UAAU,CAAC;QACX,eAAe,CAAC;QAChB,YAAY,CAAC;QACb,cAAc,KAAK,OAAO;IAC5B;AACF;AAEO,MAAM,wBAAwB;IACnC;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,WAAW;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,oBAAoB;IAAC;IAAQ;IAAU;CAAO;AAEpD,MAAM,aAAa;IAAC;IAAmB;IAAc;CAAa;AAElE,MAAM,cAAc;IACzB;QAAE,OAAO;QAAc,OAAO;IAAG;IACjC;QAAE,OAAO;QAAc,OAAO;IAAG;IACjC;QAAE,OAAO;QAAc,OAAO;IAAG;IACjC;QAAE,OAAO;QAAU,OAAO;IAAG;IAC7B;QAAE,OAAO;QAAa,OAAO;IAAG;IAChC;QAAE,OAAO;QAAW,OAAO;IAAI;CAChC;AAEM,MAAM,SAAS;IACpB,MAAM;IACN,WAAW;IACX,OAAO;IACP,YAAY;IACZ,SAAS;IACT,OAAO;IACP,UAAU;IACV,SAAS;AACX;AAEO,MAAM,gBAAgB;IAC3B,OAAO;IACP,YAAY;IACZ,SAAS;IACT,KAAK;IACL,YAAY;IACZ,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/features/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Camera, Upload, X, FileImage, Loader2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\nimport { SUPPORTED_IMAGE_TYPES, LIMITS } from '@/lib/constants';\n\ninterface ImageUploadProps {\n  onImageUpload: (file: File) => void;\n  onImageRemove?: () => void;\n  isProcessing?: boolean;\n  uploadedImage?: string;\n  className?: string;\n  maxSize?: number;\n}\n\nconst ImageUpload: React.FC<ImageUploadProps> = ({\n  onImageUpload,\n  onImageRemove,\n  isProcessing = false,\n  uploadedImage,\n  className,\n  maxSize = LIMITS.FREE_PLAN.maxImageSize,\n}) => {\n  const [error, setError] = useState<string>('');\n\n  const onDrop = useCallback(\n    (acceptedFiles: File[], rejectedFiles: any[]) => {\n      setError('');\n\n      if (rejectedFiles.length > 0) {\n        const rejection = rejectedFiles[0];\n        if (rejection.errors[0]?.code === 'file-too-large') {\n          setError(`File is too large. Maximum size is ${Math.round(maxSize / (1024 * 1024))}MB.`);\n        } else if (rejection.errors[0]?.code === 'file-invalid-type') {\n          setError('Invalid file type. Please upload an image or PDF.');\n        } else {\n          setError('File upload failed. Please try again.');\n        }\n        return;\n      }\n\n      if (acceptedFiles.length > 0) {\n        const file = acceptedFiles[0];\n        onImageUpload(file);\n      }\n    },\n    [onImageUpload, maxSize]\n  );\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.webp'],\n      'application/pdf': ['.pdf'],\n    },\n    maxSize,\n    multiple: false,\n    disabled: isProcessing,\n  });\n\n  const handleRemoveImage = () => {\n    setError('');\n    onImageRemove?.();\n  };\n\n  if (uploadedImage) {\n    return (\n      <div className={cn('relative', className)}>\n        <div className=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n          <img\n            src={uploadedImage}\n            alt=\"Uploaded note\"\n            className=\"w-full h-64 object-contain\"\n          />\n          {isProcessing && (\n            <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\n              <div className=\"text-center text-white\">\n                <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-2\" />\n                <p className=\"text-sm\">Processing image...</p>\n              </div>\n            </div>\n          )}\n          {!isProcessing && (\n            <button\n              onClick={handleRemoveImage}\n              className=\"absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 transition-colors\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={cn('w-full', className)}>\n      <div\n        {...getRootProps()}\n        className={cn(\n          'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',\n          isDragActive\n            ? 'border-blue-400 bg-blue-400/10'\n            : 'border-gray-600 hover:border-gray-500',\n          isProcessing && 'cursor-not-allowed opacity-50'\n        )}\n      >\n        <input {...getInputProps()} />\n        \n        <div className=\"flex flex-col items-center space-y-4\">\n          {isDragActive ? (\n            <Upload className=\"h-12 w-12 text-blue-400\" />\n          ) : (\n            <FileImage className=\"h-12 w-12 text-gray-400\" />\n          )}\n          \n          <div>\n            <p className=\"text-lg font-medium text-white mb-2\">\n              {isDragActive ? 'Drop your image here' : 'Upload your notes'}\n            </p>\n            <p className=\"text-sm text-gray-400 mb-4\">\n              Drag and drop an image or PDF, or click to browse\n            </p>\n            <Button variant=\"outline\" size=\"sm\" disabled={isProcessing}>\n              <Camera className=\"h-4 w-4 mr-2\" />\n              Choose File\n            </Button>\n          </div>\n          \n          <div className=\"text-xs text-gray-500\">\n            <p>Supported formats: JPEG, PNG, WebP, PDF</p>\n            <p>Maximum size: {Math.round(maxSize / (1024 * 1024))}MB</p>\n          </div>\n        </div>\n      </div>\n      \n      {error && (\n        <div className=\"mt-4 p-3 bg-red-900/50 border border-red-700 rounded-lg\">\n          <p className=\"text-sm text-red-400\">{error}</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ImageUpload;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAkBA,MAAM,cAA0C;QAAC,EAC/C,aAAa,EACb,aAAa,EACb,eAAe,KAAK,EACpB,aAAa,EACb,SAAS,EACT,UAAU,0HAAA,CAAA,SAAM,CAAC,SAAS,CAAC,YAAY,EACxC;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CACvB,CAAC,eAAuB;YACtB,SAAS;YAET,IAAI,cAAc,MAAM,GAAG,GAAG;oBAExB,oBAEO;gBAHX,MAAM,YAAY,aAAa,CAAC,EAAE;gBAClC,IAAI,EAAA,qBAAA,UAAU,MAAM,CAAC,EAAE,cAAnB,yCAAA,mBAAqB,IAAI,MAAK,kBAAkB;oBAClD,SAAS,AAAC,sCAAyE,OAApC,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,IAAI,IAAG;gBACrF,OAAO,IAAI,EAAA,sBAAA,UAAU,MAAM,CAAC,EAAE,cAAnB,0CAAA,oBAAqB,IAAI,MAAK,qBAAqB;oBAC5D,SAAS;gBACX,OAAO;oBACL,SAAS;gBACX;gBACA;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,cAAc;YAChB;QACF;0CACA;QAAC;QAAe;KAAQ;IAG1B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;aAAQ;YAC7C,mBAAmB;gBAAC;aAAO;QAC7B;QACA;QACA,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,oBAAoB;QACxB,SAAS;QACT,0BAAA,oCAAA;IACF;IAEA,IAAI,eAAe;QACjB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;sBAC7B,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,KAAI;wBACJ,WAAU;;;;;;oBAEX,8BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;oBAI5B,CAAC,8BACA,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;IAMzB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAC3B,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,eACI,mCACA,yCACJ,gBAAgB;;kCAGlB,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;yFAElB,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CAGvB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,eAAe,yBAAyB;;;;;;kDAE3C,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,6LAAC,qIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,UAAU;;0DAC5C,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAE;;;;;;kDACH,6LAAC;;4CAAE;4CAAe,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,IAAI;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;YAK3D,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAK/C;GAhIM;;QAkCkD,2KAAA,CAAA,cAAW;;;KAlC7D;uCAkIS", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Camera, Menu, X, User, Settings, LogOut } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\ninterface HeaderProps {\n  className?: string;\n}\n\nconst Header: React.FC<HeaderProps> = ({ className }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = React.useState(false);\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n  const toggleUserMenu = () => setIsUserMenuOpen(!isUserMenuOpen);\n\n  const navItems = [\n    { href: '/dashboard', label: 'Dashboard' },\n    { href: '/notes', label: 'Notes' },\n    { href: '/flashcards', label: 'Flashcards' },\n    { href: '/quizzes', label: 'Quizzes' },\n    { href: '/study', label: 'Study' },\n  ];\n\n  return (\n    <header className={cn('bg-gray-900/95 backdrop-blur-sm border-b border-gray-800', className)}>\n      <div className=\"container mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Camera className=\"h-8 w-8 text-blue-400\" />\n            <span className=\"text-2xl font-bold text-white\">SnapStudy</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-6\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-gray-300 hover:text-white transition-colors\"\n              >\n                {item.label}\n              </Link>\n            ))}\n          </nav>\n\n          {/* User Menu & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-4\">\n            {/* User Menu */}\n            <div className=\"relative\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={toggleUserMenu}\n                className=\"p-2\"\n              >\n                <User className=\"h-5 w-5\" />\n              </Button>\n\n              {isUserMenuOpen && (\n                <div className=\"absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 z-50\">\n                  <div className=\"py-1\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      <User className=\"h-4 w-4 mr-2\" />\n                      Profile\n                    </Link>\n                    <Link\n                      href=\"/settings\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      <Settings className=\"h-4 w-4 mr-2\" />\n                      Settings\n                    </Link>\n                    <hr className=\"my-1 border-gray-700\" />\n                    <button className=\"flex items-center w-full px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\">\n                      <LogOut className=\"h-4 w-4 mr-2\" />\n                      Sign Out\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleMenu}\n              className=\"md:hidden p-2\"\n            >\n              {isMenuOpen ? <X className=\"h-5 w-5\" /> : <Menu className=\"h-5 w-5\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <nav className=\"md:hidden mt-4 pb-4 border-t border-gray-800 pt-4\">\n            <div className=\"flex flex-col space-y-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-gray-300 hover:text-white transition-colors py-2\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </nav>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAYA,MAAM,SAAgC;QAAC,EAAE,SAAS,EAAE;;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE3D,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,iBAAiB,IAAM,kBAAkB,CAAC;IAEhD,MAAM,WAAW;QACf;YAAE,MAAM;YAAc,OAAO;QAAY;QACzC;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAe,OAAO;QAAa;QAC3C;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAU,OAAO;QAAQ;KAClC;IAED,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4DAA4D;kBAChF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAIlD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,IAAI;;;;;;;;;;sCAUpB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;wCAGjB,gCACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;;0EAEjC,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;;0EAEjC,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,6LAAC;wDAAG,WAAU;;;;;;kEACd,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAS7C,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;iGAAe,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAM/D,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;GAhHM;KAAA;uCAkHS", "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Camera, Github, Twitter, Mail } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface FooterProps {\n  className?: string;\n}\n\nconst Footer: React.FC<FooterProps> = ({ className }) => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { href: '/features', label: 'Features' },\n      { href: '/pricing', label: 'Pricing' },\n      { href: '/demo', label: 'Demo' },\n      { href: '/roadmap', label: 'Roadmap' },\n    ],\n    support: [\n      { href: '/help', label: 'Help Center' },\n      { href: '/contact', label: 'Contact Us' },\n      { href: '/docs', label: 'Documentation' },\n      { href: '/api', label: 'API' },\n    ],\n    legal: [\n      { href: '/privacy', label: 'Privacy Policy' },\n      { href: '/terms', label: 'Terms of Service' },\n      { href: '/cookies', label: 'Cookie Policy' },\n      { href: '/security', label: 'Security' },\n    ],\n  };\n\n  const socialLinks = [\n    { href: 'https://github.com', icon: Github, label: 'GitHub' },\n    { href: 'https://twitter.com', icon: Twitter, label: 'Twitter' },\n    { href: 'mailto:<EMAIL>', icon: Mail, label: 'Email' },\n  ];\n\n  return (\n    <footer className={cn('bg-gray-900 border-t border-gray-800', className)}>\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <Camera className=\"h-8 w-8 text-blue-400\" />\n              <span className=\"text-2xl font-bold text-white\">SnapStudy</span>\n            </Link>\n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Transform your handwritten or printed notes into interactive study materials with AI-powered digitization, summarization, and flashcard generation.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <a\n                    key={social.label}\n                    href={social.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                    aria-label={social.label}\n                    target={social.href.startsWith('http') ? '_blank' : undefined}\n                    rel={social.href.startsWith('http') ? 'noopener noreferrer' : undefined}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </a>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Product Links */}\n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Support</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal Links */}\n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"mt-12 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            &copy; {currentYear} SnapStudy. All rights reserved.\n          </p>\n          <p className=\"text-gray-400 text-sm mt-2 md:mt-0\">\n            Made with ❤️ for learners everywhere\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAMA,MAAM,SAAgC;QAAC,EAAE,SAAS,EAAE;IAClD,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAa,OAAO;YAAW;YACvC;gBAAE,MAAM;gBAAY,OAAO;YAAU;YACrC;gBAAE,MAAM;gBAAS,OAAO;YAAO;YAC/B;gBAAE,MAAM;gBAAY,OAAO;YAAU;SACtC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,OAAO;YAAc;YACtC;gBAAE,MAAM;gBAAY,OAAO;YAAa;YACxC;gBAAE,MAAM;gBAAS,OAAO;YAAgB;YACxC;gBAAE,MAAM;gBAAQ,OAAO;YAAM;SAC9B;QACD,OAAO;YACL;gBAAE,MAAM;gBAAY,OAAO;YAAiB;YAC5C;gBAAE,MAAM;gBAAU,OAAO;YAAmB;YAC5C;gBAAE,MAAM;gBAAY,OAAO;YAAgB;YAC3C;gBAAE,MAAM;gBAAa,OAAO;YAAW;SACxC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAsB,MAAM,yMAAA,CAAA,SAAM;YAAE,OAAO;QAAS;QAC5D;YAAE,MAAM;YAAuB,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO;QAAU;QAC/D;YAAE,MAAM;YAA8B,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;QAAQ;KAClE;IAED,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;kBAC5D,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;8CAElD,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAG3C,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,6LAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,cAAY,OAAO,KAAK;4CACxB,QAAQ,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,WAAW;4CACpD,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,wBAAwB;sDAE9D,cAAA,6LAAC;gDAAK,WAAU;;;;;;2CAPX,OAAO,KAAK;;;;;oCAUvB;;;;;;;;;;;;sCAKJ,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAAwB;gCAC3B;gCAAY;;;;;;;sCAEtB,6LAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;;;;;;;;;;;;AAO5D;KA9HM;uCAgIS", "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\nimport { cn } from '@/lib/utils';\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n  showHeader?: boolean;\n  showFooter?: boolean;\n  className?: string;\n}\n\nconst AppLayout: React.FC<AppLayoutProps> = ({\n  children,\n  showHeader = true,\n  showFooter = true,\n  className,\n}) => {\n  return (\n    <div className={cn('min-h-screen flex flex-col bg-gray-900', className)}>\n      {showHeader && <Header />}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      {showFooter && <Footer />}\n    </div>\n  );\n};\n\nexport default AppLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAcA,MAAM,YAAsC;QAAC,EAC3C,QAAQ,EACR,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;YAC1D,4BAAc,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACtB,6LAAC;gBAAK,WAAU;0BACb;;;;;;YAEF,4BAAc,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAG5B;KAfM;uCAiBS", "debugId": null}}, {"offset": {"line": 1286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-700 bg-gray-800/50 backdrop-blur-sm shadow-lg',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nCard.displayName = 'Card';\nCardHeader.displayName = 'CardHeader';\nCardContent.displayName = 'CardContent';\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAkBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;;;AAKP,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAChC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;;AAKP,KAAK,WAAW,GAAG;AACnB,WAAW,WAAW,GAAG;AACzB,YAAY,WAAW,GAAG;AAC1B,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, helperText, type = 'text', ...props }, ref) => {\n    const inputId = React.useId();\n\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-300\"\n          >\n            {label}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50',\n            error && 'border-red-500 focus:ring-red-500',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"text-sm text-red-400\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"text-sm text-gray-400\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAQA,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,QAAmE;QAAlE,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,MAAM,EAAE,GAAG,OAAO;;IAC/D,MAAM,UAAU,6JAAA,CAAA,UAAK,CAAC,KAAK;IAE3B,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yPACA,SAAS,qCACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;YAEtC,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAI9C;;AAGF,MAAM,WAAW,GAAG;uCAEL", "debugId": null}}, {"offset": {"line": 1452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/lib/ocr.ts"], "sourcesContent": ["import Tesseract from 'tesseract.js';\nimport { OCRResult, BoundingBox } from '@/types';\n\nexport class OCRService {\n  private static instance: OCRService;\n  private worker: Tesseract.Worker | null = null;\n\n  private constructor() {}\n\n  public static getInstance(): OCRService {\n    if (!OCRService.instance) {\n      OCRService.instance = new OCRService();\n    }\n    return OCRService.instance;\n  }\n\n  private async initializeWorker(): Promise<Tesseract.Worker> {\n    if (!this.worker) {\n      this.worker = await Tesseract.createWorker('eng');\n      await this.worker.setParameters({\n        tessedit_pageseg_mode: Tesseract.PSM.SPARSE_TEXT,\n        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?;:()[]{}\"\\'-+=*/\\\\|@#$%^&_~`<> \\n\\t',\n      });\n    }\n    return this.worker;\n  }\n\n  public async extractText(\n    imageFile: File,\n    onProgress?: (progress: number) => void\n  ): Promise<OCRResult> {\n    try {\n      const worker = await this.initializeWorker();\n      \n      const { data } = await worker.recognize(imageFile, {\n        logger: (m) => {\n          if (m.status === 'recognizing text' && onProgress) {\n            onProgress(m.progress);\n          }\n        },\n      });\n\n      const boundingBoxes: BoundingBox[] = data.words\n        .filter(word => word.confidence > 30) // Filter out low-confidence words\n        .map(word => ({\n          x: word.bbox.x0,\n          y: word.bbox.y0,\n          width: word.bbox.x1 - word.bbox.x0,\n          height: word.bbox.y1 - word.bbox.y0,\n          text: word.text,\n        }));\n\n      return {\n        text: data.text.trim(),\n        confidence: data.confidence,\n        boundingBoxes,\n      };\n    } catch (error) {\n      console.error('OCR extraction failed:', error);\n      throw new Error('Failed to extract text from image. Please try again.');\n    }\n  }\n\n  public async extractTextFromMultipleImages(\n    imageFiles: File[],\n    onProgress?: (fileIndex: number, progress: number) => void\n  ): Promise<OCRResult[]> {\n    const results: OCRResult[] = [];\n    \n    for (let i = 0; i < imageFiles.length; i++) {\n      const file = imageFiles[i];\n      const result = await this.extractText(file, (progress) => {\n        onProgress?.(i, progress);\n      });\n      results.push(result);\n    }\n    \n    return results;\n  }\n\n  public async terminate(): Promise<void> {\n    if (this.worker) {\n      await this.worker.terminate();\n      this.worker = null;\n    }\n  }\n\n  // Utility method to preprocess image for better OCR results\n  public static preprocessImage(canvas: HTMLCanvasElement): HTMLCanvasElement {\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return canvas;\n\n    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n    const data = imageData.data;\n\n    // Convert to grayscale and increase contrast\n    for (let i = 0; i < data.length; i += 4) {\n      const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;\n      \n      // Increase contrast\n      const contrast = 1.5;\n      const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));\n      const enhancedGray = factor * (gray - 128) + 128;\n      \n      data[i] = enhancedGray;     // Red\n      data[i + 1] = enhancedGray; // Green\n      data[i + 2] = enhancedGray; // Blue\n      // Alpha channel remains unchanged\n    }\n\n    ctx.putImageData(imageData, 0, 0);\n    return canvas;\n  }\n\n  // Method to convert file to canvas for preprocessing\n  public static async fileToCanvas(file: File): Promise<HTMLCanvasElement> {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n\n      if (!ctx) {\n        reject(new Error('Could not get canvas context'));\n        return;\n      }\n\n      img.onload = () => {\n        canvas.width = img.width;\n        canvas.height = img.height;\n        ctx.drawImage(img, 0, 0);\n        resolve(canvas);\n      };\n\n      img.onerror = () => {\n        reject(new Error('Failed to load image'));\n      };\n\n      img.src = URL.createObjectURL(file);\n    });\n  }\n}\n\n// Export singleton instance\nexport const ocrService = OCRService.getInstance();\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGO,MAAM;IAMX,OAAc,cAA0B;QACtC,IAAI,CAAC,WAAW,QAAQ,EAAE;YACxB,WAAW,QAAQ,GAAG,IAAI;QAC5B;QACA,OAAO,WAAW,QAAQ;IAC5B;IAEA,MAAc,mBAA8C;QAC1D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,MAAM,kJAAA,CAAA,UAAS,CAAC,YAAY,CAAC;YAC3C,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;gBAC9B,uBAAuB,kJAAA,CAAA,UAAS,CAAC,GAAG,CAAC,WAAW;gBAChD,yBAAyB;YAC3B;QACF;QACA,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,MAAa,YACX,SAAe,EACf,UAAuC,EACnB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB;YAE1C,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,OAAO,SAAS,CAAC,WAAW;gBACjD,QAAQ,CAAC;oBACP,IAAI,EAAE,MAAM,KAAK,sBAAsB,YAAY;wBACjD,WAAW,EAAE,QAAQ;oBACvB;gBACF;YACF;YAEA,MAAM,gBAA+B,KAAK,KAAK,CAC5C,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,GAAG,IAAI,kCAAkC;aACvE,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACZ,GAAG,KAAK,IAAI,CAAC,EAAE;oBACf,GAAG,KAAK,IAAI,CAAC,EAAE;oBACf,OAAO,KAAK,IAAI,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,EAAE;oBAClC,QAAQ,KAAK,IAAI,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,EAAE;oBACnC,MAAM,KAAK,IAAI;gBACjB,CAAC;YAEH,OAAO;gBACL,MAAM,KAAK,IAAI,CAAC,IAAI;gBACpB,YAAY,KAAK,UAAU;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAa,8BACX,UAAkB,EAClB,UAA0D,EACpC;QACtB,MAAM,UAAuB,EAAE;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,MAAM,OAAO,UAAU,CAAC,EAAE;YAC1B,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3C,uBAAA,iCAAA,WAAa,GAAG;YAClB;YACA,QAAQ,IAAI,CAAC;QACf;QAEA,OAAO;IACT;IAEA,MAAa,YAA2B;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS;YAC3B,IAAI,CAAC,MAAM,GAAG;QAChB;IACF;IAEA,4DAA4D;IAC5D,OAAc,gBAAgB,MAAyB,EAAqB;QAC1E,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK,OAAO;QAEjB,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACpE,MAAM,OAAO,UAAU,IAAI;QAE3B,6CAA6C;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,OAAO,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG;YAEnE,oBAAoB;YACpB,MAAM,WAAW;YACjB,MAAM,SAAS,AAAC,MAAM,CAAC,WAAW,GAAG,IAAK,CAAC,MAAM,CAAC,MAAM,QAAQ,CAAC;YACjE,MAAM,eAAe,SAAS,CAAC,OAAO,GAAG,IAAI;YAE7C,IAAI,CAAC,EAAE,GAAG,cAAkB,MAAM;YAClC,IAAI,CAAC,IAAI,EAAE,GAAG,cAAc,QAAQ;YACpC,IAAI,CAAC,IAAI,EAAE,GAAG,cAAc,OAAO;QACnC,kCAAkC;QACpC;QAEA,IAAI,YAAY,CAAC,WAAW,GAAG;QAC/B,OAAO;IACT;IAEA,qDAAqD;IACrD,aAAoB,aAAa,IAAU,EAA8B;QACvE,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAE9B,IAAI,CAAC,KAAK;gBACR,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,IAAI,MAAM,GAAG;gBACX,OAAO,KAAK,GAAG,IAAI,KAAK;gBACxB,OAAO,MAAM,GAAG,IAAI,MAAM;gBAC1B,IAAI,SAAS,CAAC,KAAK,GAAG;gBACtB,QAAQ;YACV;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;QAChC;IACF;IApIA,aAAsB;QAFtB,+KAAQ,UAAkC;IAEnB;AAqIzB;AAxIE,yKADW,YACI,YAAf,KAAA;AA2IK,MAAM,aAAa,WAAW,WAAW", "debugId": null}}, {"offset": {"line": 1580, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/app/scan/page.tsx"], "sourcesContent": ["'use client';\n\nimport ImageUpload from '@/components/features/ImageUpload';\nimport AppLayout from '@/components/layout/AppLayout';\nimport Button from '@/components/ui/Button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card';\nimport Input from '@/components/ui/Input';\nimport { SUBJECTS } from '@/lib/constants';\nimport { ocrService } from '@/lib/ocr';\nimport { OCRResult } from '@/types';\nimport { ArrowLeft, FileText, Save, Wand2 } from 'lucide-react';\nimport Link from 'next/link';\nimport { useState } from 'react';\n\n\nexport default function ScanPage() {\n  const [uploadedImage, setUploadedImage] = useState<string>('');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [ocrResult, setOcrResult] = useState<OCRResult | null>(null);\n  const [progress, setProgress] = useState(0);\n  const [noteTitle, setNoteTitle] = useState('');\n  const [selectedSubject, setSelectedSubject] = useState('');\n  const [extractedText, setExtractedText] = useState('');\n  const [aiResult, setAiResult] = useState<AIProcessingResult | null>(null);\n  const [isProcessingAI, setIsProcessingAI] = useState(false);\n  const [showFlashcards, setShowFlashcards] = useState(false);\n\n  const handleImageUpload = async (file: File) => {\n    try {\n      setIsProcessing(true);\n      setProgress(0);\n      \n      // Create preview URL\n      const imageUrl = URL.createObjectURL(file);\n      setUploadedImage(imageUrl);\n      \n      // Extract text using OCR\n      const result = await ocrService.extractText(file, (progressValue) => {\n        setProgress(Math.round(progressValue * 100));\n      });\n      \n      setOcrResult(result);\n      setExtractedText(result.text);\n      \n      // Auto-generate title from first line or filename\n      const firstLine = result.text.split('\\n')[0].trim();\n      if (firstLine && firstLine.length > 0) {\n        setNoteTitle(firstLine.substring(0, 50));\n      } else {\n        setNoteTitle(file.name.replace(/\\.[^/.]+$/, ''));\n      }\n      \n    } catch (error) {\n      console.error('OCR processing failed:', error);\n      alert('Failed to process image. Please try again.');\n    } finally {\n      setIsProcessing(false);\n      setProgress(0);\n    }\n  };\n\n  const handleImageRemove = () => {\n    if (uploadedImage) {\n      URL.revokeObjectURL(uploadedImage);\n    }\n    setUploadedImage('');\n    setOcrResult(null);\n    setExtractedText('');\n    setNoteTitle('');\n    setProgress(0);\n  };\n\n  const handleAIProcess = async () => {\n    if (!extractedText.trim()) {\n      alert('No text to process. Please scan an image first.');\n      return;\n    }\n\n    try {\n      setIsProcessingAI(true);\n      const result = await aiService.processText(extractedText, selectedSubject);\n      setAiResult(result);\n    } catch (error) {\n      console.error('AI processing failed:', error);\n      alert('Failed to process text with AI. Please try again.');\n    } finally {\n      setIsProcessingAI(false);\n    }\n  };\n\n  const handleEnhanceText = async () => {\n    if (!extractedText.trim()) return;\n\n    try {\n      setIsProcessingAI(true);\n      const enhanced = await aiService.enhanceText(extractedText);\n      setExtractedText(enhanced);\n    } catch (error) {\n      console.error('Text enhancement failed:', error);\n      alert('Failed to enhance text. Please try again.');\n    } finally {\n      setIsProcessingAI(false);\n    }\n  };\n\n  const handleSaveNote = async () => {\n    if (!ocrResult || !noteTitle.trim()) {\n      alert('Please provide a title for your note.');\n      return;\n    }\n\n    try {\n      // Here you would typically save to your backend/database\n      // For now, we'll just show a success message\n      alert('Note saved successfully!');\n\n      // Reset form\n      handleImageRemove();\n      setSelectedSubject('');\n      setAiResult(null);\n      setShowFlashcards(false);\n    } catch (error) {\n      console.error('Failed to save note:', error);\n      alert('Failed to save note. Please try again.');\n    }\n  };\n\n  const confidenceColor = (confidence: number) => {\n    if (confidence >= 80) return 'text-green-400';\n    if (confidence >= 60) return 'text-yellow-400';\n    return 'text-red-400';\n  };\n\n  return (\n    <AppLayout>\n      <div className=\"container mx-auto px-6 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/dashboard\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Dashboard\n              </Button>\n            </Link>\n            <div>\n              <h1 className=\"text-3xl font-bold text-white\">Scan Notes</h1>\n              <p className=\"text-gray-400\">Upload an image to extract and digitize your notes</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Upload Section */}\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <h2 className=\"text-xl font-semibold text-white flex items-center\">\n                  <FileText className=\"h-5 w-5 mr-2\" />\n                  Upload Image\n                </h2>\n              </CardHeader>\n              <CardContent>\n                <ImageUpload\n                  onImageUpload={handleImageUpload}\n                  onImageRemove={handleImageRemove}\n                  isProcessing={isProcessing}\n                  uploadedImage={uploadedImage}\n                />\n                \n                {isProcessing && (\n                  <div className=\"mt-4\">\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-400\">Processing...</span>\n                      <span className=\"text-sm text-white\">{progress}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                      <div \n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\" \n                        style={{ width: `${progress}%` }}\n                      />\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* OCR Results */}\n            {ocrResult && (\n              <Card>\n                <CardHeader>\n                  <h2 className=\"text-xl font-semibold text-white\">OCR Results</h2>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-400\">Confidence Score</span>\n                      <span className={`text-sm font-medium ${confidenceColor(ocrResult.confidence)}`}>\n                        {Math.round(ocrResult.confidence)}%\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-400\">Words Detected</span>\n                      <span className=\"text-sm text-white\">\n                        {ocrResult.boundingBoxes?.length || 0}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-400\">Characters</span>\n                      <span className=\"text-sm text-white\">\n                        {ocrResult.text.length}\n                      </span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n\n          {/* Text Editor Section */}\n          <div className=\"space-y-6\">\n            {ocrResult && (\n              <>\n                <Card>\n                  <CardHeader>\n                    <h2 className=\"text-xl font-semibold text-white\">Note Details</h2>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <Input\n                      label=\"Note Title\"\n                      value={noteTitle}\n                      onChange={(e) => setNoteTitle(e.target.value)}\n                      placeholder=\"Enter a title for your note\"\n                    />\n                    \n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                        Subject\n                      </label>\n                      <select\n                        value={selectedSubject}\n                        onChange={(e) => setSelectedSubject(e.target.value)}\n                        className=\"w-full rounded-lg border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      >\n                        <option value=\"\">Select a subject</option>\n                        {SUBJECTS.map((subject) => (\n                          <option key={subject} value={subject}>\n                            {subject}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card>\n                  <CardHeader>\n                    <div className=\"flex items-center justify-between\">\n                      <h2 className=\"text-xl font-semibold text-white\">Extracted Text</h2>\n                      <div className=\"flex space-x-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={handleEnhanceText}\n                          disabled={isProcessingAI}\n                        >\n                          <Wand2 className=\"h-4 w-4 mr-2\" />\n                          Enhance\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          onClick={handleAIProcess}\n                          disabled={isProcessingAI || !extractedText.trim()}\n                        >\n                          <Wand2 className=\"h-4 w-4 mr-2\" />\n                          AI Process\n                        </Button>\n                      </div>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <textarea\n                      value={extractedText}\n                      onChange={(e) => setExtractedText(e.target.value)}\n                      className=\"w-full h-64 rounded-lg border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                      placeholder=\"Extracted text will appear here...\"\n                    />\n                    \n                    <div className=\"flex justify-between items-center mt-4\">\n                      <span className=\"text-sm text-gray-400\">\n                        {extractedText.length} characters\n                      </span>\n                      <Button onClick={handleSaveNote} disabled={!noteTitle.trim()}>\n                        <Save className=\"h-4 w-4 mr-2\" />\n                        Save Note\n                      </Button>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* AI Results */}\n                {aiResult && (\n                  <Card>\n                    <CardHeader>\n                      <h2 className=\"text-xl font-semibold text-white\">AI Analysis</h2>\n                    </CardHeader>\n                    <CardContent className=\"space-y-6\">\n                      {/* Summary */}\n                      <div>\n                        <h3 className=\"text-lg font-medium text-white mb-2\">Summary</h3>\n                        <p className=\"text-gray-300 leading-relaxed\">{aiResult.summary}</p>\n                      </div>\n\n                      {/* Key Points */}\n                      <div>\n                        <h3 className=\"text-lg font-medium text-white mb-2\">Key Points</h3>\n                        <ul className=\"space-y-1\">\n                          {aiResult.keyPoints.map((point, index) => (\n                            <li key={index} className=\"text-gray-300 flex items-start\">\n                              <span className=\"text-blue-400 mr-2\">•</span>\n                              {point}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n\n                      {/* Tags */}\n                      <div>\n                        <h3 className=\"text-lg font-medium text-white mb-2\">Suggested Tags</h3>\n                        <div className=\"flex flex-wrap gap-2\">\n                          {aiResult.suggestedTags.map((tag, index) => (\n                            <span\n                              key={index}\n                              className=\"px-2 py-1 bg-blue-600/20 text-blue-400 rounded text-sm\"\n                            >\n                              {tag}\n                            </span>\n                          ))}\n                        </div>\n                      </div>\n\n                      {/* Flashcards */}\n                      <div>\n                        <div className=\"flex items-center justify-between mb-4\">\n                          <h3 className=\"text-lg font-medium text-white\">\n                            Flashcards ({aiResult.flashcards.length})\n                          </h3>\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => setShowFlashcards(!showFlashcards)}\n                          >\n                            {showFlashcards ? 'Hide' : 'Review'} Flashcards\n                          </Button>\n                        </div>\n\n                        {showFlashcards && (\n                          <FlashcardViewer\n                            flashcards={aiResult.flashcards.map((card, index) => ({\n                              id: `card-${index}`,\n                              question: card.question,\n                              answer: card.answer,\n                              difficulty: card.difficulty,\n                            }))}\n                            onComplete={(results) => {\n                              alert(`Session complete! Score: ${results.correct}/${results.total}`);\n                              setShowFlashcards(false);\n                            }}\n                          />\n                        )}\n                      </div>\n\n                      {/* Quiz Preview */}\n                      <div>\n                        <h3 className=\"text-lg font-medium text-white mb-2\">\n                          Quiz Preview ({aiResult.quiz.questions.length} questions)\n                        </h3>\n                        <div className=\"bg-gray-700/50 rounded-lg p-4\">\n                          <p className=\"text-gray-300 mb-2\">\n                            <strong>{aiResult.quiz.title}</strong>\n                          </p>\n                          <p className=\"text-sm text-gray-400\">\n                            Sample question: {aiResult.quiz.questions[0]?.question}\n                          </p>\n                          <Button variant=\"outline\" size=\"sm\" className=\"mt-2\">\n                            Take Full Quiz\n                          </Button>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                )}\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;;;AAZA;;;;;;;;;;;AAee,SAAS;QA6LC,0BAkLqB;;IA9W5C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,gBAAgB;YAChB,YAAY;YAEZ,qBAAqB;YACrB,MAAM,WAAW,IAAI,eAAe,CAAC;YACrC,iBAAiB;YAEjB,yBAAyB;YACzB,MAAM,SAAS,MAAM,oHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,MAAM,CAAC;gBACjD,YAAY,KAAK,KAAK,CAAC,gBAAgB;YACzC;YAEA,aAAa;YACb,iBAAiB,OAAO,IAAI;YAE5B,kDAAkD;YAClD,MAAM,YAAY,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;YACjD,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,aAAa,UAAU,SAAS,CAAC,GAAG;YACtC,OAAO;gBACL,aAAa,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;YAC9C;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,gBAAgB;YAChB,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,eAAe;YACjB,IAAI,eAAe,CAAC;QACtB;QACA,iBAAiB;QACjB,aAAa;QACb,iBAAiB;QACjB,aAAa;QACb,YAAY;IACd;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc,IAAI,IAAI;YACzB,MAAM;YACN;QACF;QAEA,IAAI;YACF,kBAAkB;YAClB,MAAM,SAAS,MAAM,UAAU,WAAW,CAAC,eAAe;YAC1D,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc,IAAI,IAAI;QAE3B,IAAI;YACF,kBAAkB;YAClB,MAAM,WAAW,MAAM,UAAU,WAAW,CAAC;YAC7C,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,IAAI;YACnC,MAAM;YACN;QACF;QAEA,IAAI;YACF,yDAAyD;YACzD,6CAA6C;YAC7C,MAAM;YAEN,aAAa;YACb;YACA,mBAAmB;YACnB,YAAY;YACZ,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,qBACE,6LAAC,4IAAA,CAAA,UAAS;kBACR,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI1C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAKnC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIzC,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC,gJAAA,CAAA,UAAW;oDACV,eAAe;oDACf,eAAe;oDACf,cAAc;oDACd,eAAe;;;;;;gDAGhB,8BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAK,WAAU;;wEAAsB;wEAAS;;;;;;;;;;;;;sEAEjD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,AAAC,GAAW,OAAT,UAAS;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAS1C,2BACC,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;sDAEnD,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAW,AAAC,uBAA4D,OAAtC,gBAAgB,UAAU,UAAU;;oEACzE,KAAK,KAAK,CAAC,UAAU,UAAU;oEAAE;;;;;;;;;;;;;kEAGtC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;0EACb,EAAA,2BAAA,UAAU,aAAa,cAAvB,+CAAA,yBAAyB,MAAM,KAAI;;;;;;;;;;;;kEAGxC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;0EACb,UAAU,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUpC,6LAAC;4BAAI,WAAU;sCACZ,2BACC;;kDACE,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAEnD,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC,oIAAA,CAAA,UAAK;wDACJ,OAAM;wDACN,OAAO;wDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC5C,aAAY;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6LAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEAClD,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAG;;;;;;oEAChB,0HAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,CAAC,wBACb,6LAAC;4EAAqB,OAAO;sFAC1B;2EADU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASvB,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,UAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,UAAU;;sFAEV,6LAAC,kNAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGpC,6LAAC,qIAAA,CAAA,UAAM;oEACL,MAAK;oEACL,SAAS;oEACT,UAAU,kBAAkB,CAAC,cAAc,IAAI;;sFAE/C,6LAAC,kNAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;0DAM1C,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAChD,WAAU;wDACV,aAAY;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEACb,cAAc,MAAM;oEAAC;;;;;;;0EAExB,6LAAC,qIAAA,CAAA,UAAM;gEAAC,SAAS;gEAAgB,UAAU,CAAC,UAAU,IAAI;;kFACxD,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;oCAQxC,0BACC,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;;;;;;0DAEnD,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEAErB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAiC,SAAS,OAAO;;;;;;;;;;;;kEAIhE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,6LAAC;gEAAG,WAAU;0EACX,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC;wEAAe,WAAU;;0FACxB,6LAAC;gFAAK,WAAU;0FAAqB;;;;;;4EACpC;;uEAFM;;;;;;;;;;;;;;;;kEASf,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,sBAChC,6LAAC;wEAEC,WAAU;kFAET;uEAHI;;;;;;;;;;;;;;;;kEAUb,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;;4EAAiC;4EAChC,SAAS,UAAU,CAAC,MAAM;4EAAC;;;;;;;kFAE1C,6LAAC,qIAAA,CAAA,UAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,kBAAkB,CAAC;;4EAEjC,iBAAiB,SAAS;4EAAS;;;;;;;;;;;;;4DAIvC,gCACC,6LAAC;gEACC,YAAY,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;wEACpD,IAAI,AAAC,QAAa,OAAN;wEACZ,UAAU,KAAK,QAAQ;wEACvB,QAAQ,KAAK,MAAM;wEACnB,YAAY,KAAK,UAAU;oEAC7B,CAAC;gEACD,YAAY,CAAC;oEACX,MAAM,AAAC,4BAA8C,OAAnB,QAAQ,OAAO,EAAC,KAAiB,OAAd,QAAQ,KAAK;oEAClE,kBAAkB;gEACpB;;;;;;;;;;;;kEAMN,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEAAsC;oEACnC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM;oEAAC;;;;;;;0EAEhD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFACX,cAAA,6LAAC;sFAAQ,SAAS,IAAI,CAAC,KAAK;;;;;;;;;;;kFAE9B,6LAAC;wEAAE,WAAU;;4EAAwB;6EACjB,4BAAA,SAAS,IAAI,CAAC,SAAS,CAAC,EAAE,cAA1B,gDAAA,0BAA4B,QAAQ;;;;;;;kFAExD,6LAAC,qIAAA,CAAA,UAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;kFAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe/E;GAhYwB;KAAA", "debugId": null}}]}