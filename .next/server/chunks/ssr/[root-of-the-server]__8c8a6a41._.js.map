{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900\">\n      {/* Header */}\n      <header className=\"container mx-auto px-6 py-8\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <Camera className=\"h-8 w-8 text-blue-400\" />\n            <h1 className=\"text-2xl font-bold text-white\">SnapStudy</h1>\n          </div>\n          <nav className=\"hidden md:flex space-x-6\">\n            <a href=\"#features\" className=\"text-gray-300 hover:text-white transition-colors\">Features</a>\n            <a href=\"#how-it-works\" className=\"text-gray-300 hover:text-white transition-colors\">How it Works</a>\n            <a href=\"#pricing\" className=\"text-gray-300 hover:text-white transition-colors\">Pricing</a>\n          </nav>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <main className=\"container mx-auto px-6 py-16\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <h2 className=\"text-5xl md:text-6xl font-bold text-white mb-6\">\n            Learn from Your Notes\n            <span className=\"text-blue-400\"> Instantly</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\n            Transform your handwritten or printed notes into interactive study materials with AI-powered digitization, summarization, and flashcard generation.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-16\">\n            <Link href=\"/dashboard\" className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold transition-colors flex items-center justify-center space-x-2\">\n              <Camera className=\"h-5 w-5\" />\n              <span>Start Scanning Notes</span>\n            </Link>\n            <button className=\"border border-gray-600 hover:border-gray-500 text-white px-8 py-4 rounded-lg font-semibold transition-colors\">\n              Watch Demo\n            </button>\n          </div>\n\n          {/* Feature Cards */}\n          <div className=\"grid md:grid-cols-3 gap-8 mt-16\">\n            <div className=\"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\">\n              <Camera className=\"h-12 w-12 text-blue-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold text-white mb-2\">OCR Scanner</h3>\n              <p className=\"text-gray-400\">\n                Take photos of handwritten or printed notes and instantly digitize them with advanced OCR technology.\n              </p>\n            </div>\n\n            <div className=\"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\">\n              <Brain className=\"h-12 w-12 text-purple-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold text-white mb-2\">AI Summarizer</h3>\n              <p className=\"text-gray-400\">\n                Get key points and simplified explanations from your notes using advanced AI processing.\n              </p>\n            </div>\n\n            <div className=\"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\">\n              <BookOpen className=\"h-12 w-12 text-green-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold text-white mb-2\">Smart Flashcards</h3>\n              <p className=\"text-gray-400\">\n                Automatically generate flashcards and quizzes from your content for effective studying.\n              </p>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"container mx-auto px-6 py-8 mt-16 border-t border-gray-800\">\n        <div className=\"text-center text-gray-400\">\n          <p>&copy; 2024 SnapStudy. Transform your learning experience.</p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;;;;;;;sCAEhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAY,WAAU;8CAAmD;;;;;;8CACjF,8OAAC;oCAAE,MAAK;oCAAgB,WAAU;8CAAmD;;;;;;8CACrF,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAmD;;;;;;;;;;;;;;;;;;;;;;;0BAMtF,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAO,WAAU;8CAA+G;;;;;;;;;;;;sCAMnI,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}]}