{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date);\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function calculateReadingTime(text: string): number {\n  const wordsPerMinute = 200;\n  const words = text.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading = false, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white focus:ring-gray-500',\n      ghost: 'text-gray-300 hover:bg-gray-800 hover:text-white focus:ring-gray-500',\n      destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-2 text-sm',\n      md: 'px-4 py-2 text-base',\n      lg: 'px-6 py-3 text-lg',\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          isLoading && 'cursor-not-allowed',\n          className\n        )}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading ? (\n          <>\n            <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n            </svg>\n            Loading...\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,sBACb;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,0BACC;;8BACE,8OAAC;oBAAI,WAAU;oBAAkC,MAAK;oBAAO,SAAQ;;sCACnE,8OAAC;4BAAO,WAAU;4BAAa,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,QAAO;4BAAe,aAAY;;;;;;sCACxF,8OAAC;4BAAK,WAAU;4BAAa,MAAK;4BAAe,GAAE;;;;;;;;;;;;gBAC/C;;2BAIR;;;;;;AAIR;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Camera, Menu, X, User, Settings, LogOut } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\n\ninterface HeaderProps {\n  className?: string;\n}\n\nconst Header: React.FC<HeaderProps> = ({ className }) => {\n  const [isMenuOpen, setIsMenuOpen] = React.useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = React.useState(false);\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n  const toggleUserMenu = () => setIsUserMenuOpen(!isUserMenuOpen);\n\n  const navItems = [\n    { href: '/dashboard', label: 'Dashboard' },\n    { href: '/notes', label: 'Notes' },\n    { href: '/flashcards', label: 'Flashcards' },\n    { href: '/quizzes', label: 'Quizzes' },\n    { href: '/study', label: 'Study' },\n  ];\n\n  return (\n    <header className={cn('bg-gray-900/95 backdrop-blur-sm border-b border-gray-800', className)}>\n      <div className=\"container mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Camera className=\"h-8 w-8 text-blue-400\" />\n            <span className=\"text-2xl font-bold text-white\">SnapStudy</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-6\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-gray-300 hover:text-white transition-colors\"\n              >\n                {item.label}\n              </Link>\n            ))}\n          </nav>\n\n          {/* User Menu & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-4\">\n            {/* User Menu */}\n            <div className=\"relative\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={toggleUserMenu}\n                className=\"p-2\"\n              >\n                <User className=\"h-5 w-5\" />\n              </Button>\n\n              {isUserMenuOpen && (\n                <div className=\"absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 z-50\">\n                  <div className=\"py-1\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      <User className=\"h-4 w-4 mr-2\" />\n                      Profile\n                    </Link>\n                    <Link\n                      href=\"/settings\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      <Settings className=\"h-4 w-4 mr-2\" />\n                      Settings\n                    </Link>\n                    <hr className=\"my-1 border-gray-700\" />\n                    <button className=\"flex items-center w-full px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\">\n                      <LogOut className=\"h-4 w-4 mr-2\" />\n                      Sign Out\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={toggleMenu}\n              className=\"md:hidden p-2\"\n            >\n              {isMenuOpen ? <X className=\"h-5 w-5\" /> : <Menu className=\"h-5 w-5\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <nav className=\"md:hidden mt-4 pb-4 border-t border-gray-800 pt-4\">\n            <div className=\"flex flex-col space-y-2\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-gray-300 hover:text-white transition-colors py-2\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </nav>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAYA,MAAM,SAAgC,CAAC,EAAE,SAAS,EAAE;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE3D,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,iBAAiB,IAAM,kBAAkB,CAAC;IAEhD,MAAM,WAAW;QACf;YAAE,MAAM;YAAc,OAAO;QAAY;QACzC;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAe,OAAO;QAAa;QAC3C;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAU,OAAO;QAAQ;KAClC;IAED,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4DAA4D;kBAChF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAIlD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;mCAJN,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;wCAGjB,gCACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;;0EAEjC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;;0EAEjC,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC;wDAAG,WAAU;;;;;;kEACd,8OAAC;wDAAO,WAAU;;0EAChB,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAS7C,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;iGAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAM/D,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;uCAEe", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Camera, Github, Twitter, Mail } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface FooterProps {\n  className?: string;\n}\n\nconst Footer: React.FC<FooterProps> = ({ className }) => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { href: '/features', label: 'Features' },\n      { href: '/pricing', label: 'Pricing' },\n      { href: '/demo', label: 'Demo' },\n      { href: '/roadmap', label: 'Roadmap' },\n    ],\n    support: [\n      { href: '/help', label: 'Help Center' },\n      { href: '/contact', label: 'Contact Us' },\n      { href: '/docs', label: 'Documentation' },\n      { href: '/api', label: 'API' },\n    ],\n    legal: [\n      { href: '/privacy', label: 'Privacy Policy' },\n      { href: '/terms', label: 'Terms of Service' },\n      { href: '/cookies', label: 'Cookie Policy' },\n      { href: '/security', label: 'Security' },\n    ],\n  };\n\n  const socialLinks = [\n    { href: 'https://github.com', icon: Github, label: 'GitHub' },\n    { href: 'https://twitter.com', icon: Twitter, label: 'Twitter' },\n    { href: 'mailto:<EMAIL>', icon: Mail, label: 'Email' },\n  ];\n\n  return (\n    <footer className={cn('bg-gray-900 border-t border-gray-800', className)}>\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Brand */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <Camera className=\"h-8 w-8 text-blue-400\" />\n              <span className=\"text-2xl font-bold text-white\">SnapStudy</span>\n            </Link>\n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Transform your handwritten or printed notes into interactive study materials with AI-powered digitization, summarization, and flashcard generation.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <a\n                    key={social.label}\n                    href={social.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                    aria-label={social.label}\n                    target={social.href.startsWith('http') ? '_blank' : undefined}\n                    rel={social.href.startsWith('http') ? 'noopener noreferrer' : undefined}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </a>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Product Links */}\n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Product</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Support</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal Links */}\n          <div>\n            <h3 className=\"text-white font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"mt-12 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            &copy; {currentYear} SnapStudy. All rights reserved.\n          </p>\n          <p className=\"text-gray-400 text-sm mt-2 md:mt-0\">\n            Made with ❤️ for learners everywhere\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAMA,MAAM,SAAgC,CAAC,EAAE,SAAS,EAAE;IAClD,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAa,OAAO;YAAW;YACvC;gBAAE,MAAM;gBAAY,OAAO;YAAU;YACrC;gBAAE,MAAM;gBAAS,OAAO;YAAO;YAC/B;gBAAE,MAAM;gBAAY,OAAO;YAAU;SACtC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAS,OAAO;YAAc;YACtC;gBAAE,MAAM;gBAAY,OAAO;YAAa;YACxC;gBAAE,MAAM;gBAAS,OAAO;YAAgB;YACxC;gBAAE,MAAM;gBAAQ,OAAO;YAAM;SAC9B;QACD,OAAO;YACL;gBAAE,MAAM;gBAAY,OAAO;YAAiB;YAC5C;gBAAE,MAAM;gBAAU,OAAO;YAAmB;YAC5C;gBAAE,MAAM;gBAAY,OAAO;YAAgB;YAC3C;gBAAE,MAAM;gBAAa,OAAO;YAAW;SACxC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAsB,MAAM,sMAAA,CAAA,SAAM;YAAE,OAAO;QAAS;QAC5D;YAAE,MAAM;YAAuB,MAAM,wMAAA,CAAA,UAAO;YAAE,OAAO;QAAU;QAC/D;YAAE,MAAM;YAA8B,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;QAAQ;KAClE;IAED,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;kBAC5D,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;8CAElD,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAG3C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,8OAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,cAAY,OAAO,KAAK;4CACxB,QAAQ,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,WAAW;4CACpD,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,wBAAwB;sDAE9D,cAAA,8OAAC;gDAAK,WAAU;;;;;;2CAPX,OAAO,KAAK;;;;;oCAUvB;;;;;;;;;;;;sCAKJ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAwB;gCAC3B;gCAAY;;;;;;;sCAEtB,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;;;;;;;;;;;;AAO5D;uCAEe", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\nimport { cn } from '@/lib/utils';\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n  showHeader?: boolean;\n  showFooter?: boolean;\n  className?: string;\n}\n\nconst AppLayout: React.FC<AppLayoutProps> = ({\n  children,\n  showHeader = true,\n  showFooter = true,\n  className,\n}) => {\n  return (\n    <div className={cn('min-h-screen flex flex-col bg-gray-900', className)}>\n      {showHeader && <Header />}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      {showFooter && <Footer />}\n    </div>\n  );\n};\n\nexport default AppLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAcA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;YAC1D,4BAAc,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACtB,8OAAC;gBAAK,WAAU;0BACb;;;;;;YAEF,4BAAc,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAG5B;uCAEe", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-700 bg-gray-800/50 backdrop-blur-sm shadow-lg',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\n\nCard.displayName = 'Card';\nCardHeader.displayName = 'CardHeader';\nCardContent.displayName = 'CardContent';\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAkBA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;kBAER;;;;;;AAKP,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAKP,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;AAKP,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAKP,KAAK,WAAW,GAAG;AACnB,WAAW,WAAW,GAAG;AACzB,YAAY,WAAW,GAAG;AAC1B,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/SnapStudy/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport AppLayout from '@/components/layout/AppLayout';\nimport Button from '@/components/ui/Button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/Card';\nimport { BookOpen, Brain, Camera, Clock, Plus, Target, TrendingUp } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function DashboardPage() {\n  const stats = [\n    {\n      title: 'Total Notes',\n      value: '12',\n      change: '+3 this week',\n      icon: BookOpen,\n      color: 'text-blue-400',\n    },\n    {\n      title: 'Flashcards',\n      value: '48',\n      change: '+12 this week',\n      icon: Brain,\n      color: 'text-purple-400',\n    },\n    {\n      title: 'Study Time',\n      value: '2.5h',\n      change: '+30min today',\n      icon: Clock,\n      color: 'text-green-400',\n    },\n    {\n      title: 'Accuracy',\n      value: '87%',\n      change: '+5% this week',\n      icon: Target,\n      color: 'text-yellow-400',\n    },\n  ];\n\n  const recentNotes = [\n    {\n      id: '1',\n      title: 'Calculus Chapter 3',\n      subject: 'Mathematics',\n      createdAt: '2 hours ago',\n      flashcards: 8,\n    },\n    {\n      id: '2',\n      title: 'World War II Timeline',\n      subject: 'History',\n      createdAt: '1 day ago',\n      flashcards: 12,\n    },\n    {\n      id: '3',\n      title: 'Organic Chemistry Reactions',\n      subject: 'Chemistry',\n      createdAt: '2 days ago',\n      flashcards: 15,\n    },\n  ];\n\n  return (\n    <AppLayout>\n      <div className=\"container mx-auto px-6 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-white mb-2\">Welcome back!</h1>\n          <p className=\"text-gray-400\">Ready to continue your learning journey?</p>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"mb-8\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <Link href=\"/scan\">\n              <Button className=\"flex items-center space-x-2\">\n                <Camera className=\"h-5 w-5\" />\n                <span>Scan New Notes</span>\n              </Button>\n            </Link>\n            <Button variant=\"outline\" className=\"flex items-center space-x-2\">\n              <Plus className=\"h-5 w-5\" />\n              <span>Create Flashcards</span>\n            </Button>\n            <Button variant=\"outline\" className=\"flex items-center space-x-2\">\n              <Brain className=\"h-5 w-5\" />\n              <span>Start Quiz</span>\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {stats.map((stat) => {\n            const Icon = stat.icon;\n            return (\n              <Card key={stat.title}>\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm text-gray-400 mb-1\">{stat.title}</p>\n                      <p className=\"text-2xl font-bold text-white\">{stat.value}</p>\n                      <p className=\"text-xs text-green-400 flex items-center mt-1\">\n                        <TrendingUp className=\"h-3 w-3 mr-1\" />\n                        {stat.change}\n                      </p>\n                    </div>\n                    <Icon className={`h-8 w-8 ${stat.color}`} />\n                  </div>\n                </CardContent>\n              </Card>\n            );\n          })}\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Recent Notes */}\n          <Card>\n            <CardHeader>\n              <h2 className=\"text-xl font-semibold text-white\">Recent Notes</h2>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentNotes.map((note) => (\n                  <div key={note.id} className=\"flex items-center justify-between p-4 bg-gray-700/50 rounded-lg\">\n                    <div>\n                      <h3 className=\"font-medium text-white\">{note.title}</h3>\n                      <p className=\"text-sm text-gray-400\">{note.subject} • {note.createdAt}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-sm text-blue-400\">{note.flashcards} cards</p>\n                      <Button variant=\"ghost\" size=\"sm\" className=\"mt-1\">\n                        Review\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <Button variant=\"outline\" className=\"w-full mt-4\">\n                View All Notes\n              </Button>\n            </CardContent>\n          </Card>\n\n          {/* Study Progress */}\n          <Card>\n            <CardHeader>\n              <h2 className=\"text-xl font-semibold text-white\">Study Progress</h2>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-6\">\n                <div>\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <span className=\"text-sm text-gray-400\">Daily Goal</span>\n                    <span className=\"text-sm text-white\">2.5h / 3h</span>\n                  </div>\n                  <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                    <div className=\"bg-blue-500 h-2 rounded-full\" style={{ width: '83%' }}></div>\n                  </div>\n                </div>\n\n                <div>\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <span className=\"text-sm text-gray-400\">Weekly Streak</span>\n                    <span className=\"text-sm text-white\">5 days</span>\n                  </div>\n                  <div className=\"flex space-x-1\">\n                    {[1, 2, 3, 4, 5, 6, 7].map((day) => (\n                      <div\n                        key={day}\n                        className={`w-6 h-6 rounded ${\n                          day <= 5 ? 'bg-green-500' : 'bg-gray-700'\n                        }`}\n                      />\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"pt-4\">\n                  <Button className=\"w-full\">\n                    Continue Studying\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;QACT;KACD;IAED,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,WAAW;YACX,YAAY;QACd;KACD;IAED,qBACE,8OAAC,yIAAA,CAAA,UAAS;kBACR,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;oCAAC,WAAU;;sDAChB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,8OAAC,kIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,kIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC;wBACV,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA8B,KAAK,KAAK;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAAiC,KAAK,KAAK;;;;;;8DACxD,8OAAC;oDAAE,WAAU;;sEACX,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDACrB,KAAK,MAAM;;;;;;;;;;;;;sDAGhB,8OAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;2BAXnC,KAAK,KAAK;;;;;oBAgBzB;;;;;;8BAIF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;8CAEnD,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;oDAAkB,WAAU;;sEAC3B,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA0B,KAAK,KAAK;;;;;;8EAClD,8OAAC;oEAAE,WAAU;;wEAAyB,KAAK,OAAO;wEAAC;wEAAI,KAAK,SAAS;;;;;;;;;;;;;sEAEvE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;wEAAyB,KAAK,UAAU;wEAAC;;;;;;;8EACtD,8OAAC,kIAAA,CAAA,UAAM;oEAAC,SAAQ;oEAAQ,MAAK;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;mDAP7C,KAAK,EAAE;;;;;;;;;;sDAcrB,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAc;;;;;;;;;;;;;;;;;;sCAOtD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;8CAEnD,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;gEAAK,WAAU;0EAAqB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA+B,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;;;;;;;0DAIxE,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;gEAAK,WAAU;0EAAqB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;kEACZ;4DAAC;4DAAG;4DAAG;4DAAG;4DAAG;4DAAG;4DAAG;yDAAE,CAAC,GAAG,CAAC,CAAC,oBAC1B,8OAAC;gEAEC,WAAW,CAAC,gBAAgB,EAC1B,OAAO,IAAI,iBAAiB,eAC5B;+DAHG;;;;;;;;;;;;;;;;0DASb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,UAAM;oDAAC,WAAU;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7C", "debugId": null}}]}