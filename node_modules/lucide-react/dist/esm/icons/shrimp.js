/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M11 12h.01", key: "1lr4k6" }],
  ["path", { d: "M13 22c.5-.5 1.12-1 2.5-1-1.38 0-2-.5-2.5-1", key: "fatpdi" }],
  [
    "path",
    {
      d: "M14 2a3.28 3.28 0 0 1-3.227 1.798l-6.17-.561A2.387 2.387 0 1 0 4.387 8H15.5a1 1 0 0 1 0 13 1 1 0 0 0 0-5H12a7 7 0 0 1-7-7V8",
      key: "kehrqe"
    }
  ],
  ["path", { d: "M14 8a8.5 8.5 0 0 1 0 8", key: "1imjx2" }],
  ["path", { d: "M16 16c2 0 4.5-4 4-6", key: "z0nejz" }]
];
const Shrimp = createLucideIcon("shrimp", __iconNode);

export { __iconNode, Shrimp as default };
//# sourceMappingURL=shrimp.js.map
