/**
 * @license lucide-react v0.533.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M15.295 19.562 16 22", key: "31jsb7" }],
  ["path", { d: "m17 16 3.758 2.098", key: "121ar7" }],
  ["path", { d: "m19 12.5 3.026-.598", key: "19ukd3" }],
  [
    "path",
    {
      d: "M7.61 6.3a3 3 0 0 0-3.92 1.3l-1.38 2.79a3 3 0 0 0 1.3 3.91l6.89 3.597a1 1 0 0 0 1.342-.447l3.106-6.211a1 1 0 0 0-.447-1.341z",
      key: "lwb9l9"
    }
  ],
  ["path", { d: "M8 9V2", key: "1xa0v7" }]
];
const Spotlight = createLucideIcon("spotlight", __iconNode);

export { __iconNode, Spotlight as default };
//# sourceMappingURL=spotlight.js.map
