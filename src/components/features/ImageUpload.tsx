'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Camera, Upload, X, FileImage, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { SUPPORTED_IMAGE_TYPES, LIMITS } from '@/lib/constants';

interface ImageUploadProps {
  onImageUpload: (file: File) => void;
  onImageRemove?: () => void;
  isProcessing?: boolean;
  uploadedImage?: string;
  className?: string;
  maxSize?: number;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageUpload,
  onImageRemove,
  isProcessing = false,
  uploadedImage,
  className,
  maxSize = LIMITS.FREE_PLAN.maxImageSize,
}) => {
  const [error, setError] = useState<string>('');

  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: any[]) => {
      setError('');

      if (rejectedFiles.length > 0) {
        const rejection = rejectedFiles[0];
        if (rejection.errors[0]?.code === 'file-too-large') {
          setError(`File is too large. Maximum size is ${Math.round(maxSize / (1024 * 1024))}MB.`);
        } else if (rejection.errors[0]?.code === 'file-invalid-type') {
          setError('Invalid file type. Please upload an image or PDF.');
        } else {
          setError('File upload failed. Please try again.');
        }
        return;
      }

      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        onImageUpload(file);
      }
    },
    [onImageUpload, maxSize]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp'],
      'application/pdf': ['.pdf'],
    },
    maxSize,
    multiple: false,
    disabled: isProcessing,
  });

  const handleRemoveImage = () => {
    setError('');
    onImageRemove?.();
  };

  if (uploadedImage) {
    return (
      <div className={cn('relative', className)}>
        <div className="relative bg-gray-800 rounded-lg overflow-hidden">
          <img
            src={uploadedImage}
            alt="Uploaded note"
            className="w-full h-64 object-contain"
          />
          {isProcessing && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <div className="text-center text-white">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                <p className="text-sm">Processing image...</p>
              </div>
            </div>
          )}
          {!isProcessing && (
            <button
              onClick={handleRemoveImage}
              className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('w-full', className)}>
      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
          isDragActive
            ? 'border-blue-400 bg-blue-400/10'
            : 'border-gray-600 hover:border-gray-500',
          isProcessing && 'cursor-not-allowed opacity-50'
        )}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          {isDragActive ? (
            <Upload className="h-12 w-12 text-blue-400" />
          ) : (
            <FileImage className="h-12 w-12 text-gray-400" />
          )}
          
          <div>
            <p className="text-lg font-medium text-white mb-2">
              {isDragActive ? 'Drop your image here' : 'Upload your notes'}
            </p>
            <p className="text-sm text-gray-400 mb-4">
              Drag and drop an image or PDF, or click to browse
            </p>
            <Button variant="outline" size="sm" disabled={isProcessing}>
              <Camera className="h-4 w-4 mr-2" />
              Choose File
            </Button>
          </div>
          
          <div className="text-xs text-gray-500">
            <p>Supported formats: JPEG, PNG, WebP, PDF</p>
            <p>Maximum size: {Math.round(maxSize / (1024 * 1024))}MB</p>
          </div>
        </div>
      </div>
      
      {error && (
        <div className="mt-4 p-3 bg-red-900/50 border border-red-700 rounded-lg">
          <p className="text-sm text-red-400">{error}</p>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
