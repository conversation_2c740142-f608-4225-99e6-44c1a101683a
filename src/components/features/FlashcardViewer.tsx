'use client';

import React, { useState } from 'react';
import { RotateCcw, ChevronLeft, ChevronRight, Eye, EyeOff } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { cn } from '@/lib/utils';

interface FlashcardData {
  id: string;
  question: string;
  answer: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface FlashcardViewerProps {
  flashcards: FlashcardData[];
  onComplete?: (results: { correct: number; total: number }) => void;
  className?: string;
}

const FlashcardViewer: React.FC<FlashcardViewerProps> = ({
  flashcards,
  onComplete,
  className,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [results, setResults] = useState<Record<string, 'correct' | 'incorrect' | null>>({});

  const currentCard = flashcards[currentIndex];
  const isLastCard = currentIndex === flashcards.length - 1;
  const isFirstCard = currentIndex === 0;

  const handleNext = () => {
    if (currentIndex < flashcards.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setIsFlipped(false);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setIsFlipped(false);
    }
  };

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleResult = (result: 'correct' | 'incorrect') => {
    const newResults = { ...results, [currentCard.id]: result };
    setResults(newResults);

    if (isLastCard) {
      // Calculate final results
      const correct = Object.values(newResults).filter(r => r === 'correct').length;
      const total = flashcards.length;
      onComplete?.({ correct, total });
    } else {
      handleNext();
    }
  };

  const resetSession = () => {
    setCurrentIndex(0);
    setIsFlipped(false);
    setResults({});
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'hard': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getProgressPercentage = () => {
    return ((currentIndex + 1) / flashcards.length) * 100;
  };

  if (flashcards.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <p className="text-gray-400">No flashcards available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-400">
            Card {currentIndex + 1} of {flashcards.length}
          </span>
          <span className={`text-sm font-medium ${getDifficultyColor(currentCard.difficulty)}`}>
            {currentCard.difficulty}
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${getProgressPercentage()}%` }}
          />
        </div>
      </div>

      {/* Flashcard */}
      <div className="relative">
        <Card className="min-h-[300px] cursor-pointer" onClick={handleFlip}>
          <CardContent className="p-8 flex flex-col justify-center items-center text-center h-full">
            <div className="space-y-4">
              {!isFlipped ? (
                <>
                  <div className="flex items-center justify-center mb-4">
                    <Eye className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-400">Question</span>
                  </div>
                  <h3 className="text-xl font-medium text-white leading-relaxed">
                    {currentCard.question}
                  </h3>
                  <p className="text-sm text-gray-400 mt-6">
                    Click to reveal answer
                  </p>
                </>
              ) : (
                <>
                  <div className="flex items-center justify-center mb-4">
                    <EyeOff className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-400">Answer</span>
                  </div>
                  <h3 className="text-lg font-medium text-blue-400 leading-relaxed">
                    {currentCard.answer}
                  </h3>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Flip indicator */}
        <div className="absolute top-4 right-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleFlip();
            }}
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Controls */}
      <div className="space-y-4">
        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={isFirstCard}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <Button
            variant="ghost"
            onClick={resetSession}
            size="sm"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>

          <Button
            variant="outline"
            onClick={handleNext}
            disabled={isLastCard}
          >
            Next
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>

        {/* Answer Feedback (only show when flipped) */}
        {isFlipped && (
          <div className="flex justify-center space-x-4">
            <Button
              variant="outline"
              onClick={() => handleResult('incorrect')}
              className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
            >
              Incorrect
            </Button>
            <Button
              onClick={() => handleResult('correct')}
              className="bg-green-600 hover:bg-green-700"
            >
              Correct
            </Button>
          </div>
        )}
      </div>

      {/* Results Summary */}
      {Object.keys(results).length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">Session Progress</span>
              <div className="flex space-x-4 text-sm">
                <span className="text-green-400">
                  ✓ {Object.values(results).filter(r => r === 'correct').length}
                </span>
                <span className="text-red-400">
                  ✗ {Object.values(results).filter(r => r === 'incorrect').length}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FlashcardViewer;
