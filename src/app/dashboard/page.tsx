'use client';

import AppLayout from '@/components/layout/AppLayout';
import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { BookOpen, Brain, Camera, Clock, Plus, Target, TrendingUp } from 'lucide-react';
import Link from 'next/link';

export default function DashboardPage() {
  const stats = [
    {
      title: 'Total Notes',
      value: '12',
      change: '+3 this week',
      icon: BookOpen,
      color: 'text-blue-400',
    },
    {
      title: 'Flashcards',
      value: '48',
      change: '+12 this week',
      icon: Brain,
      color: 'text-purple-400',
    },
    {
      title: 'Study Time',
      value: '2.5h',
      change: '+30min today',
      icon: Clock,
      color: 'text-green-400',
    },
    {
      title: 'Accuracy',
      value: '87%',
      change: '+5% this week',
      icon: Target,
      color: 'text-yellow-400',
    },
  ];

  const recentNotes = [
    {
      id: '1',
      title: 'Calculus Chapter 3',
      subject: 'Mathematics',
      createdAt: '2 hours ago',
      flashcards: 8,
    },
    {
      id: '2',
      title: 'World War II Timeline',
      subject: 'History',
      createdAt: '1 day ago',
      flashcards: 12,
    },
    {
      id: '3',
      title: 'Organic Chemistry Reactions',
      subject: 'Chemistry',
      createdAt: '2 days ago',
      flashcards: 15,
    },
  ];

  return (
    <AppLayout>
      <div className="container mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Welcome back!</h1>
          <p className="text-gray-400">Ready to continue your learning journey?</p>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <Link href="/scan">
              <Button className="flex items-center space-x-2">
                <Camera className="h-5 w-5" />
                <span>Scan New Notes</span>
              </Button>
            </Link>
            <Button variant="outline" className="flex items-center space-x-2">
              <Plus className="h-5 w-5" />
              <span>Create Flashcards</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2">
              <Brain className="h-5 w-5" />
              <span>Start Quiz</span>
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat) => {
            const Icon = stat.icon;
            return (
              <Card key={stat.title}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-400 mb-1">{stat.title}</p>
                      <p className="text-2xl font-bold text-white">{stat.value}</p>
                      <p className="text-xs text-green-400 flex items-center mt-1">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {stat.change}
                      </p>
                    </div>
                    <Icon className={`h-8 w-8 ${stat.color}`} />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Notes */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold text-white">Recent Notes</h2>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentNotes.map((note) => (
                  <div key={note.id} className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                    <div>
                      <h3 className="font-medium text-white">{note.title}</h3>
                      <p className="text-sm text-gray-400">{note.subject} • {note.createdAt}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-blue-400">{note.flashcards} cards</p>
                      <Button variant="ghost" size="sm" className="mt-1">
                        Review
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="outline" className="w-full mt-4">
                View All Notes
              </Button>
            </CardContent>
          </Card>

          {/* Study Progress */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold text-white">Study Progress</h2>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-400">Daily Goal</span>
                    <span className="text-sm text-white">2.5h / 3h</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '83%' }}></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-400">Weekly Streak</span>
                    <span className="text-sm text-white">5 days</span>
                  </div>
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5, 6, 7].map((day) => (
                      <div
                        key={day}
                        className={`w-6 h-6 rounded ${
                          day <= 5 ? 'bg-green-500' : 'bg-gray-700'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                <div className="pt-4">
                  <Button className="w-full">
                    Continue Studying
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
