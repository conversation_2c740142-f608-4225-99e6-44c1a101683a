'use client';

import <PERSON>cardViewer from '@/components/features/FlashcardViewer';
import ImageUpload from '@/components/features/ImageUpload';
import AppLayout from '@/components/layout/AppLayout';
import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import { aiService } from '@/lib/ai';
import { SUBJECTS } from '@/lib/constants';
import { ocrService } from '@/lib/ocr';
import { AIProcessingResult, OCRResult } from '@/types';
import { ArrowLeft, FileText, Save, Wand2 } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';


export default function ScanPage() {
  const [uploadedImage, setUploadedImage] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [ocrResult, setOcrResult] = useState<OCRResult | null>(null);
  const [progress, setProgress] = useState(0);
  const [noteTitle, setNoteTitle] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [extractedText, setExtractedText] = useState('');
  const [aiResult, setAiResult] = useState<AIProcessingResult | null>(null);
  const [isProcessingAI, setIsProcessingAI] = useState(false);
  const [showFlashcards, setShowFlashcards] = useState(false);

  const handleImageUpload = async (file: File) => {
    try {
      setIsProcessing(true);
      setProgress(0);
      
      // Create preview URL
      const imageUrl = URL.createObjectURL(file);
      setUploadedImage(imageUrl);
      
      // Extract text using OCR
      const result = await ocrService.extractText(file, (progressValue) => {
        setProgress(Math.round(progressValue * 100));
      });
      
      setOcrResult(result);
      setExtractedText(result.text);
      
      // Auto-generate title from first line or filename
      const firstLine = result.text.split('\n')[0].trim();
      if (firstLine && firstLine.length > 0) {
        setNoteTitle(firstLine.substring(0, 50));
      } else {
        setNoteTitle(file.name.replace(/\.[^/.]+$/, ''));
      }
      
    } catch (error) {
      console.error('OCR processing failed:', error);
      alert('Failed to process image. Please try again.');
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  };

  const handleImageRemove = () => {
    if (uploadedImage) {
      URL.revokeObjectURL(uploadedImage);
    }
    setUploadedImage('');
    setOcrResult(null);
    setExtractedText('');
    setNoteTitle('');
    setProgress(0);
  };

  const handleAIProcess = async () => {
    if (!extractedText.trim()) {
      alert('No text to process. Please scan an image first.');
      return;
    }

    try {
      setIsProcessingAI(true);
      const result = await aiService.processText(extractedText, selectedSubject);
      setAiResult(result);
    } catch (error) {
      console.error('AI processing failed:', error);
      alert('Failed to process text with AI. Please try again.');
    } finally {
      setIsProcessingAI(false);
    }
  };

  const handleEnhanceText = async () => {
    if (!extractedText.trim()) return;

    try {
      setIsProcessingAI(true);
      const enhanced = await aiService.enhanceText(extractedText);
      setExtractedText(enhanced);
    } catch (error) {
      console.error('Text enhancement failed:', error);
      alert('Failed to enhance text. Please try again.');
    } finally {
      setIsProcessingAI(false);
    }
  };

  const handleSaveNote = async () => {
    if (!ocrResult || !noteTitle.trim()) {
      alert('Please provide a title for your note.');
      return;
    }

    try {
      // Here you would typically save to your backend/database
      // For now, we'll just show a success message
      alert('Note saved successfully!');

      // Reset form
      handleImageRemove();
      setSelectedSubject('');
      setAiResult(null);
      setShowFlashcards(false);
    } catch (error) {
      console.error('Failed to save note:', error);
      alert('Failed to save note. Please try again.');
    }
  };

  const confidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-400';
    if (confidence >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <AppLayout>
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-white">Scan Notes</h1>
              <p className="text-gray-400">Upload an image to extract and digitize your notes</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Section */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <h2 className="text-xl font-semibold text-white flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Upload Image
                </h2>
              </CardHeader>
              <CardContent>
                <ImageUpload
                  onImageUpload={handleImageUpload}
                  onImageRemove={handleImageRemove}
                  isProcessing={isProcessing}
                  uploadedImage={uploadedImage}
                />
                
                {isProcessing && (
                  <div className="mt-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-400">Processing...</span>
                      <span className="text-sm text-white">{progress}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* OCR Results */}
            {ocrResult && (
              <Card>
                <CardHeader>
                  <h2 className="text-xl font-semibold text-white">OCR Results</h2>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-400">Confidence Score</span>
                      <span className={`text-sm font-medium ${confidenceColor(ocrResult.confidence)}`}>
                        {Math.round(ocrResult.confidence)}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-400">Words Detected</span>
                      <span className="text-sm text-white">
                        {ocrResult.boundingBoxes?.length || 0}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-400">Characters</span>
                      <span className="text-sm text-white">
                        {ocrResult.text.length}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Text Editor Section */}
          <div className="space-y-6">
            {ocrResult && (
              <>
                <Card>
                  <CardHeader>
                    <h2 className="text-xl font-semibold text-white">Note Details</h2>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Input
                      label="Note Title"
                      value={noteTitle}
                      onChange={(e) => setNoteTitle(e.target.value)}
                      placeholder="Enter a title for your note"
                    />
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Subject
                      </label>
                      <select
                        value={selectedSubject}
                        onChange={(e) => setSelectedSubject(e.target.value)}
                        className="w-full rounded-lg border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">Select a subject</option>
                        {SUBJECTS.map((subject) => (
                          <option key={subject} value={subject}>
                            {subject}
                          </option>
                        ))}
                      </select>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <h2 className="text-xl font-semibold text-white">Extracted Text</h2>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleEnhanceText}
                          disabled={isProcessingAI}
                        >
                          <Wand2 className="h-4 w-4 mr-2" />
                          Enhance
                        </Button>
                        <Button
                          size="sm"
                          onClick={handleAIProcess}
                          disabled={isProcessingAI || !extractedText.trim()}
                        >
                          <Wand2 className="h-4 w-4 mr-2" />
                          AI Process
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <textarea
                      value={extractedText}
                      onChange={(e) => setExtractedText(e.target.value)}
                      className="w-full h-64 rounded-lg border border-gray-600 bg-gray-800 px-3 py-2 text-sm text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      placeholder="Extracted text will appear here..."
                    />
                    
                    <div className="flex justify-between items-center mt-4">
                      <span className="text-sm text-gray-400">
                        {extractedText.length} characters
                      </span>
                      <Button onClick={handleSaveNote} disabled={!noteTitle.trim()}>
                        <Save className="h-4 w-4 mr-2" />
                        Save Note
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* AI Results */}
                {aiResult && (
                  <Card>
                    <CardHeader>
                      <h2 className="text-xl font-semibold text-white">AI Analysis</h2>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Summary */}
                      <div>
                        <h3 className="text-lg font-medium text-white mb-2">Summary</h3>
                        <p className="text-gray-300 leading-relaxed">{aiResult.summary}</p>
                      </div>

                      {/* Key Points */}
                      <div>
                        <h3 className="text-lg font-medium text-white mb-2">Key Points</h3>
                        <ul className="space-y-1">
                          {aiResult.keyPoints.map((point, index) => (
                            <li key={index} className="text-gray-300 flex items-start">
                              <span className="text-blue-400 mr-2">•</span>
                              {point}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Tags */}
                      <div>
                        <h3 className="text-lg font-medium text-white mb-2">Suggested Tags</h3>
                        <div className="flex flex-wrap gap-2">
                          {aiResult.suggestedTags.map((tag, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-blue-600/20 text-blue-400 rounded text-sm"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Flashcards */}
                      <div>
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-lg font-medium text-white">
                            Flashcards ({aiResult.flashcards.length})
                          </h3>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowFlashcards(!showFlashcards)}
                          >
                            {showFlashcards ? 'Hide' : 'Review'} Flashcards
                          </Button>
                        </div>

                        {showFlashcards && (
                          <FlashcardViewer
                            flashcards={aiResult.flashcards.map((card, index) => ({
                              id: `card-${index}`,
                              question: card.question,
                              answer: card.answer,
                              difficulty: card.difficulty,
                            }))}
                            onComplete={(results) => {
                              alert(`Session complete! Score: ${results.correct}/${results.total}`);
                              setShowFlashcards(false);
                            }}
                          />
                        )}
                      </div>

                      {/* Quiz Preview */}
                      <div>
                        <h3 className="text-lg font-medium text-white mb-2">
                          Quiz Preview ({aiResult.quiz.questions.length} questions)
                        </h3>
                        <div className="bg-gray-700/50 rounded-lg p-4">
                          <p className="text-gray-300 mb-2">
                            <strong>{aiResult.quiz.title}</strong>
                          </p>
                          <p className="text-sm text-gray-400">
                            Sample question: {aiResult.quiz.questions[0]?.question}
                          </p>
                          <Button variant="outline" size="sm" className="mt-2">
                            Take Full Quiz
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
