// Application constants

export const APP_CONFIG = {
  name: 'SnapStudy',
  description: 'Learn from Your Notes Instantly',
  version: '0.1.0',
  author: 'SnapStudy Team',
} as const;

export const LIMITS = {
  FREE_PLAN: {
    maxNotes: 10,
    maxFlashcards: 50,
    maxQuizzes: 5,
    maxImageSize: 5 * 1024 * 1024, // 5MB
  },
  PRO_PLAN: {
    maxNotes: -1, // unlimited
    maxFlashcards: -1, // unlimited
    maxQuizzes: -1, // unlimited
    maxImageSize: 20 * 1024 * 1024, // 20MB
  },
} as const;

export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'application/pdf',
] as const;

export const SUBJECTS = [
  'Mathematics',
  'Science',
  'History',
  'Literature',
  'Languages',
  'Computer Science',
  'Business',
  'Medicine',
  'Law',
  'Engineering',
  'Art',
  'Music',
  'Other',
] as const;

export const DIFFICULTY_LEVELS = ['easy', 'medium', 'hard'] as const;

export const QUIZ_TYPES = ['multiple-choice', 'true-false', 'fill-blank'] as const;

export const STUDY_GOALS = [
  { label: '15 minutes', value: 15 },
  { label: '30 minutes', value: 30 },
  { label: '45 minutes', value: 45 },
  { label: '1 hour', value: 60 },
  { label: '1.5 hours', value: 90 },
  { label: '2 hours', value: 120 },
] as const;

export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  NOTES: '/notes',
  FLASHCARDS: '/flashcards',
  QUIZZES: '/quizzes',
  STUDY: '/study',
  SETTINGS: '/settings',
  PROFILE: '/profile',
} as const;

export const API_ENDPOINTS = {
  NOTES: '/api/notes',
  FLASHCARDS: '/api/flashcards',
  QUIZZES: '/api/quizzes',
  OCR: '/api/ocr',
  AI_PROCESS: '/api/ai/process',
  UPLOAD: '/api/upload',
} as const;
